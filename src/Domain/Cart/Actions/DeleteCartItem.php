<?php

namespace Domain\Cart\Actions;

use App\Models\MarketplaceCartItem;
use App\Models\User;
use Domain\Cart\DataTransferObjects\UpdateCartData;

class DeleteCartItem
{
    /*********************************************************************
     * DELETE CART ITEM - DELETE CART ITEM
     *********************************************************************
     *
     * Deletes a cart item from the database.
     *
     *********************************************************************/
    public function handleUserCartItem(UpdateCartData $payload, User $user): bool
    {
        try {

            $cartWebsiteId  = $payload->cartWebsiteId;
            $userId         = $user->id;

            MarketplaceCartItem::where('user_id', $userId)
                ->where('marketplace_website_id', $cartWebsiteId)
                ->delete();

            return true;
        } catch (\Throwable $th) {
            throw $th;
        }
    }


    /*********************************************************************
     * DELETE CART ITEMS - DELETE CART ITEMS
     *********************************************************************
     *
     * Deletes cart items from the database.
     *
     *********************************************************************/
    public function handleCartItems(array $itemsIds): bool
    {
        try {
            MarketplaceCartItem::destroy($itemsIds);
            return true;
        } catch (\Throwable $th) {
            throw $th;
        }
    }
}
