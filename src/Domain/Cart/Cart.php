<?php

namespace Domain\Cart;

use Domain\Cart\Actions\CreateCart;
use Domain\Cart\Actions\UpdateCart;
use Domain\Cart\Actions\DeleteCartItem;
use Domain\Cart\List\GetUserCart;
use Illuminate\Support\Facades\Auth;
use Domain\Cart\DataTransferObjects\CartData;
use Domain\Cart\DataTransferObjects\UpdateCartData;
use Throwable;

class Cart
{

    /*************************************************
     * Add to Cart
    /************************************************/
    public function addToCart(CartData $payload): bool
    {
        return (new CreateCart())($payload, Auth::user());
    }

    /*************************************************
     * Update Cart Item
    /************************************************/
    public function updateCart(UpdateCartData $payload): bool
    {
        return (new UpdateCart())($payload, Auth::user());
    }

    /*************************************************
     * Delete Cart Items
    /************************************************/
    public function deleteCartItems(array $itemsIds): bool|Throwable
    {
        return (new DeleteCartItem())->handleCartItems($itemsIds);
    }

    /*************************************************
     * Get User Cart
    /************************************************/
    public static function getUserCart(): array
    {
        return GetUserCart::handle(Auth::user());
    }
}
