<template>
  <div class="rich-text-editor border border-[#6b7280] rounded-lg overflow-hidden" :class="{ 'border-red-500': hasError }">
    <!-- Toolbar -->
    <div v-if="editor" class="border-b border-gray-200 bg-gray-50 p-2">
      <div class="flex flex-wrap items-center gap-1">
        <!-- Bold -->
        <button
          type="button"
          @click="editor.chain().focus().toggleBold().run()"
          :class="{ 'bg-gray-200': editor.isActive('bold') }"
          class="p-2 rounded hover:bg-gray-200 transition-colors"
          title="Bold (Ctrl+B)"
        >
          <svg fill="#000000" class="w-4 h-4" viewBox="0 0 38 38" version="1.1" xmlns="http://www.w3.org/2000/svg"><g id="SVGRepo_bgCarrier" stroke-width="0"></g><g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g><g id="SVGRepo_iconCarrier"> <title>bold</title> <path d="M24.898 17.352c-0.574-0.716-1.286-1.297-2.099-1.707l-0.036-0.017c0.54-0.409 0.999-0.889 1.371-1.433l0.014-0.021c0.72-1.038 1.151-2.325 1.151-3.712 0-0.024-0-0.048-0-0.072l0 0.004c0-0.023 0-0.050 0-0.077 0-1.766-0.754-3.356-1.957-4.466l-0.004-0.004c-1.3-1.157-3.023-1.864-4.911-1.864-0.138 0-0.276 0.004-0.412 0.011l0.019-0.001h-5.999c-0.012-0-0.022-0.007-0.034-0.007h-7.307c-0.69 0-1.25 0.56-1.25 1.25s0.56 1.25 1.25 1.25v0h2.589v18.998h-2.589c-0.69 0-1.25 0.56-1.25 1.25s0.56 1.25 1.25 1.25v0h3.341c0.017 0.001 0.031 0.010 0.048 0.010h10.975c0.117 0.007 0.253 0.010 0.39 0.010 1.893 0 3.619-0.715 4.923-1.889l-0.007 0.006c1.208-1.139 1.96-2.751 1.96-4.538 0-0.042-0-0.083-0.001-0.125l0 0.006c0-0.022 0-0.049 0-0.075 0-1.535-0.537-2.944-1.434-4.050l0.009 0.012zM9.81 25.465v-8.384h9.247c0.091-0.006 0.197-0.010 0.303-0.010 1.233 0 2.351 0.495 3.166 1.297l-0.001-0.001c0.763 0.773 1.235 1.836 1.235 3.009 0 0.020-0 0.040-0 0.061l0-0.003c0.001 0.024 0.001 0.052 0.001 0.081 0 1.091-0.46 2.076-1.196 2.769l-0.002 0.002c-0.843 0.741-1.956 1.193-3.174 1.193-0.128 0-0.254-0.005-0.38-0.015l0.017 0.001zM9.81 6.521h8.192c0.098-0.007 0.213-0.011 0.329-0.011 1.222 0 2.341 0.436 3.212 1.161l-0.008-0.007c0.739 0.663 1.202 1.621 1.202 2.687 0 0.026-0 0.052-0.001 0.077l0-0.004c0 0.019 0 0.042 0 0.064 0 1.096-0.407 2.098-1.077 2.861l0.004-0.005c-0.606 0.74-1.519 1.208-2.542 1.208-0.034 0-0.068-0.001-0.102-0.002l0.005 0h-9.216z"></path> </g></svg>
        </button>

        <!-- Italic -->
        <button
          type="button"
          @click="editor.chain().focus().toggleItalic().run()"
          :class="{ 'bg-gray-200': editor.isActive('italic') }"
          class="p-2 rounded hover:bg-gray-200 transition-colors"
          title="Italic (Ctrl+I)"
        >
          <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
            <path d="M10 4v3h2.21l-3.42 8H6v3h8v-3h-2.21l3.42-8H18V4h-8z"/>
          </svg>

        </button>

        <!-- Underline -->
        <button
          type="button"
          @click="editor.chain().focus().toggleUnderline().run()"
          :class="{ 'bg-gray-200': editor.isActive('underline') }"
          class="p-2 rounded hover:bg-gray-200 transition-colors"
          title="Underline (Ctrl+U)"
        >
          <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
            <path d="M12 17c3.31 0 6-2.69 6-6V3h-2.5v8c0 1.93-1.57 3.5-3.5 3.5S8.5 12.93 8.5 11V3H6v8c0 3.31 2.69 6 6 6zm-7 2v2h14v-2H5z"/>
          </svg>
        </button>

        <!-- Separator -->
        <div class="w-px h-6 bg-gray-300 mx-1"></div>

        <!-- Bullet List -->
        <button
          type="button"
          @click="editor.chain().focus().toggleBulletList().run()"
          :class="{ 'bg-gray-200': editor.isActive('bulletList') }"
          class="p-2 rounded hover:bg-gray-200 transition-colors"
          title="Bullet List"
        >
          <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
            <path d="M4 10.5c-.83 0-1.5.67-1.5 1.5s.67 1.5 1.5 1.5 1.5-.67 1.5-1.5-.67-1.5-1.5-1.5zm0-6c-.83 0-1.5.67-1.5 1.5S3.17 7.5 4 7.5 5.5 6.83 5.5 6 4.83 4.5 4 4.5zm0 12c-.83 0-1.5.67-1.5 1.5s.67 1.5 1.5 1.5 1.5-.67 1.5-1.5-.67-1.5-1.5-1.5zM7 19h14v-2H7v2zm0-6h14v-2H7v2zm0-8v2h14V5H7z"/>
          </svg>
        </button>

        <!-- Numbered List -->
        <button
          type="button"
          @click="editor.chain().focus().toggleOrderedList().run()"
          :class="{ 'bg-gray-200': editor.isActive('orderedList') }"
          class="p-2 rounded hover:bg-gray-200 transition-colors"
          title="Numbered List"
        >
          <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
            <path d="M2 17h2v.5H3v1h1v.5H2v1h3v-4H2v1zm1-9h1V4H2v1h1v3zm-1 3h1.8L2 13.1v.9h3v-1H3.2L5 10.9V10H2v1zm5-6v2h14V5H7zm0 14h14v-2H7v2zm0-6h14v-2H7v2z"/>
          </svg>
        </button>

        <!-- Separator -->
        <div class="w-px h-6 bg-gray-300 mx-1"></div>

        <!-- Link -->
        <button
          type="button"
          @click="toggleLink"
          :class="{ 'bg-gray-200': editor.isActive('link') }"
          class="p-2 rounded hover:bg-gray-200 transition-colors"
          title="Add Link"
        >
          <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
            <path d="M3.9 12c0-1.71 1.39-3.1 3.1-3.1h4V7H7c-2.76 0-5 2.24-5 5s2.24 5 5 5h4v-1.9H7c-1.71 0-3.1-1.39-3.1-3.1zM8 13h8v-2H8v2zm9-6h-4v1.9h4c1.71 0 3.1 1.39 3.1 3.1s-1.39 3.1-3.1 3.1h-4V17h4c2.76 0 5-2.24 5-5s-2.24-5-5-5z"/>
          </svg>
        </button>

        <!-- Separator -->
        <div class="w-px h-6 bg-gray-300 mx-1"></div>

        <!-- Undo -->
        <button
          type="button"
          @click="editor.chain().focus().undo().run()"
          :disabled="!editor.can().undo()"
          class="p-2 rounded hover:bg-gray-200 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          title="Undo (Ctrl+Z)"
        >
          <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
            <path d="M12.5 8c-2.65 0-5.05.99-6.9 2.6L2 7v9h9l-3.62-3.62c1.39-1.16 3.16-1.88 5.12-1.88 3.54 0 6.55 2.31 7.6 5.5l2.37-.78C21.08 11.03 17.15 8 12.5 8z"/>
          </svg>
        </button>

        <!-- Redo -->
        <button
          type="button"
          @click="editor.chain().focus().redo().run()"
          :disabled="!editor.can().redo()"
          class="p-2 rounded hover:bg-gray-200 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          title="Redo (Ctrl+Y)"
        >
          <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
            <path d="M18.4 10.6C16.55 8.99 14.15 8 11.5 8c-4.65 0-8.58 3.03-9.96 7.22L3.9 16c1.05-3.19 4.05-5.5 7.6-5.5 1.95 0 3.73.72 5.12 1.88L13 16h9V7l-3.6 3.6z"/>
          </svg>
        </button>
      </div>
    </div>

    <!-- Editor Content -->
    <div
      ref="editorElement"
      class="min-h-[150px] p-4 focus-within:ring-0 overflow-y-auto max-h-[200px]"
    ></div>

    <!-- Error Message -->
    <div v-if="hasError && errorMessage" class="text-red-500 text-sm mt-1 px-4 pb-2">
      {{ errorMessage }}
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount, watch, nextTick } from 'vue'
import { Editor } from '@tiptap/vue-3'
import StarterKit from '@tiptap/starter-kit'
import Placeholder from '@tiptap/extension-placeholder'

const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  },
  placeholder: {
    type: String,
    default: 'Start typing...'
  },
  hasError: {
    type: Boolean,
    default: false
  },
  errorMessage: {
    type: String,
    default: ''
  },
  disabled: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:modelValue'])

const editorElement = ref(null)
const editor = ref(null)

onMounted(async () => {
  await nextTick()
  
  editor.value = new Editor({
    element: editorElement.value,
    extensions: [
      StarterKit,
      Placeholder.configure({
        placeholder: props.placeholder,
      }),
    ],
    content: props.modelValue,
    editable: !props.disabled,
    onUpdate: ({ editor }) => {
      emit('update:modelValue', editor.getHTML())
    },
  })
})

onBeforeUnmount(() => {
  if (editor.value) {
    editor.value.destroy()
  }
})

// Watch for external changes to modelValue
watch(() => props.modelValue, (newValue) => {
  if (editor.value && editor.value.getHTML() !== newValue) {
    editor.value.commands.setContent(newValue, false)
  }
})

// Watch for disabled state changes
watch(() => props.disabled, (newDisabled) => {
  if (editor.value) {
    editor.value.setEditable(!newDisabled)
  }
})

// Link functionality
const toggleLink = () => {
  if (editor.value.isActive('link')) {
    editor.value.chain().focus().unsetLink().run()
  } else {
    const url = window.prompt('Enter URL:')
    if (url) {
      editor.value.chain().focus().setLink({ href: url }).run()
    }
  }
}
</script>

<style scoped>
/* Basic editor styling */
:deep(.ProseMirror) {
  outline: none;
  min-height: 120px;
  padding: 0.5rem;
}

/* Placeholder styling */
:deep(.ProseMirror p.is-editor-empty:first-child::before) {
  content: attr(data-placeholder);
  float: left;
  color: #9ca3af;
  pointer-events: none;
  height: 0;
}

/* Basic list styling */
:deep(.ProseMirror ul) {
  list-style-type: disc;
  margin-left: 1.5rem;
}

:deep(.ProseMirror ol) {
  list-style-type: decimal;
  margin-left: 1.5rem;
}

:deep(.ProseMirror li) {
  margin: 0.25rem 0;
}
</style>
