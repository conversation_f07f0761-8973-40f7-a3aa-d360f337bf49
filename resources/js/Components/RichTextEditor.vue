<template>
  <div class="rich-text-editor">
    <!-- Toolbar -->
    <div class="toolbar border border-gray-300 rounded-t-lg bg-gray-50 p-2 flex items-center gap-1 flex-wrap">
      <!-- Bold -->
      <button
        type="button"
        @click="editor.chain().focus().toggleBold().run()"
        :class="{ 'is-active': editor?.isActive('bold') }"
        class="toolbar-button"
        title="Bold (Ctrl+B)"
      >
        <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
          <path d="M5 3v14h5.5c2.5 0 4.5-2 4.5-4.5 0-1.5-.8-2.8-2-3.5 1.2-.7 2-2 2-3.5C15 3 13 1 10.5 1H5v2zm2 2h3.5c1.4 0 2.5 1.1 2.5 2.5S11.9 10 10.5 10H7V5zm0 7h4c1.7 0 3 1.3 3 3s-1.3 3-3 3H7v-6z"/>
        </svg>
      </button>

      <!-- Italic -->
      <button
        type="button"
        @click="editor.chain().focus().toggleItalic().run()"
        :class="{ 'is-active': editor?.isActive('italic') }"
        class="toolbar-button"
        title="Italic (Ctrl+I)"
      >
        <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
          <path d="M8 1h8v2h-2.5l-3 12H13v2H5v-2h2.5l3-12H8V1z"/>
        </svg>
      </button>

      <!-- Underline -->
      <button
        type="button"
        @click="editor.chain().focus().toggleUnderline().run()"
        :class="{ 'is-active': editor?.isActive('underline') }"
        class="toolbar-button"
        title="Underline (Ctrl+U)"
      >
        <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
          <path d="M10 18c3.3 0 6-2.7 6-6V2h-2v10c0 2.2-1.8 4-4 4s-4-1.8-4-4V2H4v10c0 3.3 2.7 6 6 6zM3 20h14v-2H3v2z"/>
        </svg>
      </button>

      <!-- Separator -->
      <div class="w-px h-6 bg-gray-300 mx-1"></div>

      <!-- Bullet List -->
      <button
        type="button"
        @click="editor.chain().focus().toggleBulletList().run()"
        :class="{ 'is-active': editor?.isActive('bulletList') }"
        class="toolbar-button"
        title="Bullet List"
      >
        <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
          <path d="M4 6a2 2 0 100-4 2 2 0 000 4zM4 12a2 2 0 100-4 2 2 0 000 4zM4 18a2 2 0 100-4 2 2 0 000 4zM8 5h10v2H8V5zM8 11h10v2H8v-2zM8 17h10v2H8v-2z"/>
        </svg>
      </button>

      <!-- Numbered List -->
      <button
        type="button"
        @click="editor.chain().focus().toggleOrderedList().run()"
        :class="{ 'is-active': editor?.isActive('orderedList') }"
        class="toolbar-button"
        title="Numbered List"
      >
        <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
          <path d="M3 4h1v1H3V4zM3 7h1v1H3V7zM3 10h1v1H3v-1zM6 4h12v2H6V4zM6 8h12v2H6V8zM6 12h12v2H6v-2z"/>
        </svg>
      </button>

      <!-- Separator -->
      <div class="w-px h-6 bg-gray-300 mx-1"></div>

      <!-- Link -->
      <button
        type="button"
        @click="toggleLink"
        :class="{ 'is-active': editor?.isActive('link') }"
        class="toolbar-button"
        title="Add Link"
      >
        <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
          <path d="M12.586 4.586a2 2 0 112.828 2.828l-3 3a2 2 0 01-2.828 0 1 1 0 00-1.414 1.414 4 4 0 005.656 0l3-3a4 4 0 00-5.656-5.656l-1.5 1.5a1 1 0 101.414 1.414l1.5-1.5zm-5 5a2 2 0 012.828 0 1 1 0 101.414-1.414 4 4 0 00-5.656 0l-3 3a4 4 0 105.656 5.656l1.5-1.5a1 1 0 10-1.414-1.414l-1.5 1.5a2 2 0 11-2.828-2.828l3-3z"/>
        </svg>
      </button>

      <!-- Separator -->
      <div class="w-px h-6 bg-gray-300 mx-1"></div>

      <!-- Undo -->
      <button
        type="button"
        @click="editor.chain().focus().undo().run()"
        :disabled="!editor?.can().undo()"
        class="toolbar-button"
        title="Undo (Ctrl+Z)"
      >
        <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
          <path d="M3 10a7 7 0 0112.93-4H14a1 1 0 000 2h4a1 1 0 001-1V3a1 1 0 00-2 0v1.07A9 9 0 103 10z"/>
        </svg>
      </button>

      <!-- Redo -->
      <button
        type="button"
        @click="editor.chain().focus().redo().run()"
        :disabled="!editor?.can().redo()"
        class="toolbar-button"
        title="Redo (Ctrl+Y)"
      >
        <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
          <path d="M17 10a7 7 0 00-12.93-4H6a1 1 0 000 2H2a1 1 0 00-1-1V3a1 1 0 002 0v1.07A9 9 0 1117 10z"/>
        </svg>
      </button>
    </div>

    <!-- Editor Content -->
    <div 
      ref="editorElement"
      class="editor-content border border-t-0 border-gray-300 rounded-b-lg min-h-[150px] p-3 focus-within:ring-2 focus-within:ring-blue-500 focus-within:border-blue-500"
      :class="{ 'border-red-500': hasError }"
    ></div>

    <!-- Error Message -->
    <div v-if="hasError && errorMessage" class="text-red-500 text-sm mt-1">
      {{ errorMessage }}
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount, watch, nextTick } from 'vue'
import { Editor } from '@tiptap/vue-3'
import StarterKit from '@tiptap/starter-kit'
import Link from '@tiptap/extension-link'
import Placeholder from '@tiptap/extension-placeholder'
import Underline from '@tiptap/extension-underline'

const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  },
  placeholder: {
    type: String,
    default: 'Start typing...'
  },
  hasError: {
    type: Boolean,
    default: false
  },
  errorMessage: {
    type: String,
    default: ''
  },
  disabled: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:modelValue'])

const editorElement = ref(null)
const editor = ref(null)

onMounted(async () => {
  await nextTick()
  
  editor.value = new Editor({
    element: editorElement.value,
    extensions: [
      StarterKit,
      Underline,
      Link.configure({
        openOnClick: false,
        HTMLAttributes: {
          class: 'text-blue-600 underline hover:text-blue-800',
        },
      }),
      Placeholder.configure({
        placeholder: props.placeholder,
      }),
    ],
    content: props.modelValue,
    editable: !props.disabled,
    onUpdate: ({ editor }) => {
      emit('update:modelValue', editor.getHTML())
    },
    editorProps: {
      attributes: {
        class: 'prose prose-sm max-w-none focus:outline-none',
      },
    },
  })
})

onBeforeUnmount(() => {
  if (editor.value) {
    editor.value.destroy()
  }
})

// Watch for external changes to modelValue
watch(() => props.modelValue, (newValue) => {
  if (editor.value && editor.value.getHTML() !== newValue) {
    editor.value.commands.setContent(newValue, false)
  }
})

// Watch for disabled state changes
watch(() => props.disabled, (newDisabled) => {
  if (editor.value) {
    editor.value.setEditable(!newDisabled)
  }
})

// Link functionality
const toggleLink = () => {
  if (editor.value.isActive('link')) {
    editor.value.chain().focus().unsetLink().run()
  } else {
    const url = window.prompt('Enter URL:')
    if (url) {
      editor.value.chain().focus().setLink({ href: url }).run()
    }
  }
}
</script>

<style scoped>
.toolbar-button {
  @apply p-2 rounded hover:bg-gray-200 transition-colors duration-150 disabled:opacity-50 disabled:cursor-not-allowed;
}

.toolbar-button.is-active {
  @apply bg-blue-100 text-blue-700;
}

/* Editor content styling */
:deep(.ProseMirror) {
  outline: none;
  min-height: 120px;
}

:deep(.ProseMirror p.is-editor-empty:first-child::before) {
  content: attr(data-placeholder);
  float: left;
  color: #9ca3af;
  pointer-events: none;
  height: 0;
}

:deep(.ProseMirror ul) {
  list-style-type: disc;
  margin-left: 1.5rem;
}

:deep(.ProseMirror ol) {
  list-style-type: decimal;
  margin-left: 1.5rem;
}

:deep(.ProseMirror li) {
  margin: 0.25rem 0;
}

:deep(.ProseMirror strong) {
  font-weight: bold;
}

:deep(.ProseMirror em) {
  font-style: italic;
}

:deep(.ProseMirror u) {
  text-decoration: underline;
}

:deep(.ProseMirror a) {
  color: #2563eb;
  text-decoration: underline;
}

:deep(.ProseMirror a:hover) {
  color: #1d4ed8;
}
</style>
