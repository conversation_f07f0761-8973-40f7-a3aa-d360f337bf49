<template>
  <div class="rich-text-editor">
    <!-- Editor Content with Default Tiptap Styling -->
    <div
      ref="editorElement"
      class="border border-gray-300 rounded-lg min-h-[150px] p-3 focus-within:ring-2 focus-within:ring-blue-500 focus-within:border-blue-500"
      :class="{ 'border-red-500': hasError }"
    ></div>

    <!-- Error Message -->
    <div v-if="hasError && errorMessage" class="text-red-500 text-sm mt-1">
      {{ errorMessage }}
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount, watch, nextTick } from 'vue'
import { Editor } from '@tiptap/vue-3'
import StarterKit from '@tiptap/starter-kit'
import Placeholder from '@tiptap/extension-placeholder'

const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  },
  placeholder: {
    type: String,
    default: 'Start typing...'
  },
  hasError: {
    type: Boolean,
    default: false
  },
  errorMessage: {
    type: String,
    default: ''
  },
  disabled: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:modelValue'])

const editorElement = ref(null)
const editor = ref(null)

onMounted(async () => {
  await nextTick()
  
  editor.value = new Editor({
    element: editorElement.value,
    extensions: [
      StarterKit,
      Placeholder.configure({
        placeholder: props.placeholder,
      }),
    ],
    content: props.modelValue,
    editable: !props.disabled,
    onUpdate: ({ editor }) => {
      emit('update:modelValue', editor.getHTML())
    },
  })
})

onBeforeUnmount(() => {
  if (editor.value) {
    editor.value.destroy()
  }
})

// Watch for external changes to modelValue
watch(() => props.modelValue, (newValue) => {
  if (editor.value && editor.value.getHTML() !== newValue) {
    editor.value.commands.setContent(newValue, false)
  }
})

// Watch for disabled state changes
watch(() => props.disabled, (newDisabled) => {
  if (editor.value) {
    editor.value.setEditable(!newDisabled)
  }
})
</script>

<style scoped>
/* Basic editor styling */
:deep(.ProseMirror) {
  outline: none;
  min-height: 120px;
  padding: 0.5rem;
}

/* Placeholder styling */
:deep(.ProseMirror p.is-editor-empty:first-child::before) {
  content: attr(data-placeholder);
  float: left;
  color: #9ca3af;
  pointer-events: none;
  height: 0;
}

/* Basic list styling */
:deep(.ProseMirror ul) {
  list-style-type: disc;
  margin-left: 1.5rem;
}

:deep(.ProseMirror ol) {
  list-style-type: decimal;
  margin-left: 1.5rem;
}

:deep(.ProseMirror li) {
  margin: 0.25rem 0;
}
</style>
