<template>
    <div class="space-y-6">
        <!-- Header Section -->
        <div class="border-b pb-4">
            <h3 class="text-lg font-medium text-gray-900">Requirements Details</h3>
            <p class="mt-1 text-sm text-gray-500">Review the requirements provided by the customer.</p>
        </div>

        <div class="space-y-4">
            <!-- Article Topic -->
            <div class="bg-white border border-gray-200 rounded-lg p-4">
                <div class="flex items-start justify-between">
                    <div class="flex-1">
                        <h4 class="text-sm font-medium text-gray-900 mb-2 flex items-center">
                            <svg class="w-4 h-4 mr-2 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                            </svg>
                            Article Topic
                        </h4>
                        <p class="text-sm text-gray-700 leading-relaxed">
                            {{ requirements?.article_topic || 'Not provided' }}
                        </p>
                    </div>
                    <span v-if="requirements?.article_topic" class="ml-3 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                        Provided
                    </span>
                    <span v-else class="ml-3 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                        Not Set
                    </span>
                </div>
            </div>

            <!-- Anchor Text -->
            <div class="bg-white border border-gray-200 rounded-lg p-4">
                <div class="flex items-start justify-between">
                    <div class="flex-1">
                        <h4 class="text-sm font-medium text-gray-900 mb-2 flex items-center">
                            <svg class="w-4 h-4 mr-2 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"></path>
                            </svg>
                            Anchor Text
                        </h4>
                        <div class="text-sm text-gray-700 leading-relaxed">
                            <span v-if="requirements?.anchor_text" class="inline-flex items-center px-2 py-1 bg-blue-50 text-blue-700 rounded-md font-medium">
                                "{{ requirements.anchor_text }}"
                            </span>
                            <span v-else class="text-gray-500">Not provided</span>
                        </div>
                    </div>
                    <span v-if="requirements?.anchor_text" class="ml-3 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                        Provided
                    </span>
                    <span v-else class="ml-3 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                        Not Set
                    </span>
                </div>
            </div>

            <!-- Advertiser URL -->
            <div class="bg-white border border-gray-200 rounded-lg p-4">
                <div class="flex items-start justify-between">
                    <div class="flex-1">
                        <h4 class="text-sm font-medium text-gray-900 mb-2 flex items-center">
                            <svg class="w-4 h-4 mr-2 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"></path>
                            </svg>
                            Advertiser URL
                        </h4>
                        <div class="text-sm leading-relaxed">
                            <a v-if="requirements?.advertiser_url"
                               :href="requirements.advertiser_url"
                               target="_blank"
                               class="inline-flex items-center text-indigo-600 hover:text-indigo-800 hover:underline break-all">
                                <svg class="w-3 h-3 mr-1 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"></path>
                                </svg>
                                {{ requirements.advertiser_url }}
                            </a>
                            <span v-else class="text-gray-500">Not provided</span>
                        </div>
                    </div>
                    <span v-if="requirements?.advertiser_url" class="ml-3 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                        Provided
                    </span>
                    <span v-else class="ml-3 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                        Not Set
                    </span>
                </div>
            </div>

            <!-- Additional Comments -->
            <div v-if="requirements?.requirement_comments" class="bg-white border border-gray-200 rounded-lg overflow-hidden">
                <div class="px-4 py-3 bg-gray-50 border-b border-gray-200 flex items-center justify-between">
                    <h4 class="text-sm font-medium text-gray-900 flex items-center">
                        <svg class="w-4 h-4 mr-2 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-3l-4 4z"></path>
                        </svg>
                        Additional Comments
                    </h4>
                    <button
                        @click="toggleCommentsExpanded"
                        class="text-xs text-gray-500 hover:text-gray-700 flex items-center gap-1"
                        v-if="requirements?.requirement_comments && requirements.requirement_comments.length > 200"
                    >
                        <span>{{ isCommentsExpanded ? 'Collapse' : 'Expand' }}</span>
                        <svg class="w-3 h-3 transition-transform" :class="{ 'rotate-180': isCommentsExpanded }" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                        </svg>
                    </button>
                </div>
                <div class="relative">
                    <div
                        class="p-4 text-sm text-gray-700 leading-relaxed overflow-hidden transition-all duration-300"
                        :class="{
                            'max-h-32': !isCommentsExpanded && requirements?.requirement_comments && requirements.requirement_comments.length > 200,
                            'max-h-none': isCommentsExpanded || !requirements?.requirement_comments || requirements.requirement_comments.length <= 200
                        }"
                    >
                        {{ requirements.requirement_comments }}
                    </div>
                    <!-- Fade overlay for collapsed comments -->
                    <div
                        v-if="!isCommentsExpanded && requirements?.requirement_comments && requirements.requirement_comments.length > 200"
                        class="absolute bottom-0 left-0 right-0 h-8 bg-gradient-to-t from-white to-transparent pointer-events-none"
                    ></div>
                </div>
            </div>

            <!-- Summary Card -->
            <div class="bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-lg p-4">
                <h4 class="text-sm font-medium text-blue-900 mb-3 flex items-center">
                    <svg class="w-4 h-4 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    Requirements Summary
                </h4>
                <div class="grid grid-cols-2 gap-4 text-xs">
                    <div class="flex justify-between">
                        <span class="text-blue-700">Article Topic:</span>
                        <span class="font-medium" :class="requirements?.article_topic ? 'text-green-700' : 'text-gray-500'">
                            {{ requirements?.article_topic ? '✓ Set' : '✗ Missing' }}
                        </span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-blue-700">Anchor Text:</span>
                        <span class="font-medium" :class="requirements?.anchor_text ? 'text-green-700' : 'text-gray-500'">
                            {{ requirements?.anchor_text ? '✓ Set' : '✗ Missing' }}
                        </span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-blue-700">Target URL:</span>
                        <span class="font-medium" :class="requirements?.advertiser_url ? 'text-green-700' : 'text-gray-500'">
                            {{ requirements?.advertiser_url ? '✓ Set' : '✗ Missing' }}
                        </span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-blue-700">Comments:</span>
                        <span class="font-medium" :class="requirements?.requirement_comments ? 'text-green-700' : 'text-gray-500'">
                            {{ requirements?.requirement_comments ? '✓ Provided' : '✗ None' }}
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref } from 'vue'

defineProps({
    requirements: {
        type: Object,
        required: true
    }
})

// Reactive state for expand/collapse functionality
const isCommentsExpanded = ref(false)

const toggleCommentsExpanded = () => {
    isCommentsExpanded.value = !isCommentsExpanded.value
}
</script>