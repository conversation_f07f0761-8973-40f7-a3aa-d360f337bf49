<template>
    <div class="space-y-6">
        <!-- Header Section -->
        <div class="border-b pb-4">
            <h3 class="text-lg font-medium text-gray-900">Content Details</h3>
            <p class="mt-1 text-sm text-gray-500">Review the content provided by the customer.</p>
        </div>

        <div v-if="content" class="space-y-6">
            <!-- Title Section -->
            <div class="bg-white border border-gray-200 rounded-lg p-4">
                <h4 class="text-sm font-medium text-gray-900 mb-2">Title</h4>
                <p class="text-sm text-gray-700 leading-relaxed">{{ content?.title || 'No title provided' }}</p>
            </div>

            <!-- Content URL Section -->
            <div v-if="content?.content_url" class="bg-white border border-gray-200 rounded-lg p-4">
                <h4 class="text-sm font-medium text-gray-900 mb-2">Content URL</h4>
                <a :href="content.content_url" target="_blank"
                   class="inline-flex items-center text-sm text-indigo-600 hover:text-indigo-800 hover:underline break-all">
                    <svg class="w-4 h-4 mr-1 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"></path>
                    </svg>
                    {{ content.content_url }}
                </a>
            </div>

            <!-- Content Body Section with Improved Layout -->
            <div class="bg-white border border-gray-200 rounded-lg overflow-hidden">
                <div class="px-4 py-3 bg-gray-50 border-b border-gray-200 flex items-center justify-between">
                    <h4 class="text-sm font-medium text-gray-900">Content</h4>
                    <button
                        @click="toggleExpanded"
                        class="text-xs text-gray-500 hover:text-gray-700 flex items-center gap-1"
                        v-if="content?.content_body && content.content_body.length > 500"
                    >
                        <span>{{ isExpanded ? 'Collapse' : 'Expand' }}</span>
                        <svg class="w-3 h-3 transition-transform" :class="{ 'rotate-180': isExpanded }" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                        </svg>
                    </button>
                </div>
                <div class="relative">
                    <div
                        class="p-4 prose prose-sm max-w-none overflow-hidden transition-all duration-300"
                        :class="{
                            'max-h-40': !isExpanded && content?.content_body && content.content_body.length > 500,
                            'max-h-none': isExpanded || !content?.content_body || content.content_body.length <= 500
                        }"
                    >
                        <div v-html="content?.content_body || 'No content provided'" class="leading-relaxed"></div>
                    </div>
                    <!-- Fade overlay for collapsed content -->
                    <div
                        v-if="!isExpanded && content?.content_body && content.content_body.length > 500"
                        class="absolute bottom-0 left-0 right-0 h-16 bg-gradient-to-t from-white to-transparent pointer-events-none"
                    ></div>
                </div>
            </div>

            <!-- Attached Files Section -->
            <div v-if="media?.length" class="bg-white border border-gray-200 rounded-lg overflow-hidden">
                <div class="px-4 py-3 bg-gray-50 border-b border-gray-200">
                    <h4 class="text-sm font-medium text-gray-900">Attached Files</h4>
                </div>
                <div class="p-4">
                    <div class="grid grid-cols-1 gap-3 sm:grid-cols-2">
                        <div v-for="file in media" :key="file.id"
                             class="flex items-center justify-between p-3 bg-gray-50 rounded-lg border border-gray-200 hover:bg-gray-100 transition-colors">
                            <div class="flex items-center space-x-3 min-w-0 flex-1">
                                <div class="flex-shrink-0">
                                    <svg class="h-8 w-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                    </svg>
                                </div>
                                <div class="min-w-0 flex-1">
                                    <p class="text-sm font-medium text-gray-900 truncate">{{ file.name }}</p>
                                    <p class="text-xs text-gray-500">{{ formatFileSize(file.size) }}</p>
                                </div>
                            </div>
                            <a :href="file.url" target="_blank"
                               class="flex-shrink-0 inline-flex items-center px-3 py-1.5 text-xs font-medium text-indigo-700 bg-indigo-100 hover:bg-indigo-200 rounded-md transition-colors">
                                <svg class="h-3 w-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"></path>
                                </svg>
                                View
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Comments Section -->
            <div v-if="content?.comments" class="bg-white border border-gray-200 rounded-lg p-4">
                <h4 class="text-sm font-medium text-gray-900 mb-2">Comments</h4>
                <p class="text-sm text-gray-700 leading-relaxed">{{ content.comments }}</p>
            </div>

            <!-- Metadata Grid -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <!-- Content Source & Writer -->
                <div class="bg-white border border-gray-200 rounded-lg p-4">
                    <h4 class="text-sm font-medium text-gray-900 mb-3">Content Details</h4>
                    <div class="space-y-3">
                        <div>
                            <dt class="text-xs font-medium text-gray-500 uppercase tracking-wide">Source</dt>
                            <dd class="mt-1 text-sm text-gray-900">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                                      :class="content?.content_source === 'customer' ? 'bg-blue-100 text-blue-800' : 'bg-green-100 text-green-800'">
                                    {{ content?.content_source === 'customer' ? 'Customer' : 'Team' }}
                                </span>
                            </dd>
                        </div>
                        <div>
                            <dt class="text-xs font-medium text-gray-500 uppercase tracking-wide">Writer</dt>
                            <dd class="mt-1 text-sm text-gray-900">{{ content?.writer?.name || 'Not assigned' }}</dd>
                        </div>
                    </div>
                </div>

                <!-- Timestamps -->
                <div class="bg-white border border-gray-200 rounded-lg p-4">
                    <h4 class="text-sm font-medium text-gray-900 mb-3">Timeline</h4>
                    <div class="space-y-3">
                        <div>
                            <dt class="text-xs font-medium text-gray-500 uppercase tracking-wide">Created</dt>
                            <dd class="mt-1 text-sm text-gray-900">{{ content?.created_at_formatted || 'N/A' }}</dd>
                        </div>
                        <div>
                            <dt class="text-xs font-medium text-gray-500 uppercase tracking-wide">Last Updated</dt>
                            <dd class="mt-1 text-sm text-gray-900">{{ content?.updated_at_formatted || 'N/A' }}</dd>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div v-else class="text-center py-4">
            <p class="text-gray-500">No content available</p>
        </div>
    </div>
</template>

<script setup>
import { ref } from 'vue'

defineProps({
    media: {
        type: Array,
        required: true,
        default: () => ([])
    },
    content: {
        type: Object,
        required: true,
        default: () => ({})
    }
})

// Reactive state for expand/collapse functionality
const isExpanded = ref(false)

const toggleExpanded = () => {
    isExpanded.value = !isExpanded.value
}

const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};
</script>
